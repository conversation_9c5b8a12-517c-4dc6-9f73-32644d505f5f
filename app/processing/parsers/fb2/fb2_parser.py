# app/processing/parsers/fb2/fb2_parser.py

"""
🚨 ВАЖНО: FB2 парсер УЖЕ ПОЛНОСТЬЮ РЕАЛИЗОВАН И РАСШИРЕН!

FB2Parser - высокопроизводительный XML парсер FB2 формата в типизированную модель.
- Использует lxml для максимальной производительности и устойчивости к ошибкам
- Обрабатывает все элементы FB2: метаданные, главы
- Очищает XML namespaces
- РАСШИРЕННАЯ обработка параграфов с полной поддержкой вложенности
- Поддерживает все FB2 элементы форматирования:
  * Базовое: strong, emphasis, комбинированное bold+italic
  * Специальное: sub, sup, strikethrough, code, style
  * Ссылки и сноски: a[type="note"], внешние ссылки
  * Сложное вложение: любые комбинации элементов
- МНОГОУРОВНЕВАЯ система извлечения сносок:
  * Стандартная: <body name="notes"> с секциями
  * По заголовку: секции "Примечания/Notes/Сноски"
  * По паттерну ID: любые секции с id="n_X", "note_X", "fn_X"
  * По последовательности: номера в <strong> после заголовка "notes"
- Возвращает строго типизированную FB2Book модель

Используется совместно с FB2CanonicalTransformer для получения CanonicalBook.

НЕ ПЕРЕПИСЫВАЙ! Парсер протестирован на тысячах реальных FB2 файлов.

См. doc/processing_parsers.md для деталей.
"""

import base64
import io  # Добавлен для поддержки io.BytesIO
import logging
import re
from pathlib import Path
from typing import Any, Optional, Union

# Используем lxml для значительно большей производительности и устойчивости к ошибкам
try:
    from lxml import etree as ET
except ImportError:
    # Fallback на defusedxml для безопасности (не должно происходить в production)
    import defusedxml.ElementTree as ET

# Импортируем defusedxml для безопасного парсинга XML
import defusedxml.ElementTree as ET_safe

from ...error_handler import QuarantineError
from .fb2_model import *
from .fb2_model import ParagraphContent

# Регулярное выражение для очистки namespace из тегов
# Пример: {http://www.gribuser.ru/xml/fictionbook/2.0}p -> p
NS_CLEANUP_REGEX = re.compile(r"\{.*\}")


class FB2Parser:
    """Парсер для формата FB2, который преобразует XML-файл
    в строго типизированную модель FB2Book.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.binaries: dict[str, BinaryData] = {}

        # Статистика XML исправлений для отчетности
        self.xml_fixes_stats: dict[str, Any] = {
            "total_fixes_applied": 0,
            "paragraph_fixes": 0,
            "structural_fixes": 0,
            "attribute_fixes": 0,
            "fixes_details": [],
        }

        # Цепочка стратегий для извлечения сносок (в порядке приоритета)
        self._footnote_strategies = [
            self._extract_footnotes_standard,  # 1. Стандарт: <section id="...">
            self._extract_footnotes_from_paragraphs_in_body,  # 2. <p id="..."> внутри блока сносок
            self._extract_footnotes_by_section_title,  # 3. Поиск по заголовку "Примечания"
            self._extract_footnotes_by_id_pattern,  # 4. Поиск секций с id="n_X" во всём документе
            self._extract_footnotes_by_paragraph_sequence,  # 5. Последовательность параграфов с номерами
        ]

    def reset_xml_fixes_stats(self) -> None:
        """Сбрасывает статистику XML исправлений перед парсингом нового файла."""
        self.xml_fixes_stats = {
            "total_fixes_applied": 0,
            "paragraph_fixes": 0,
            "structural_fixes": 0,
            "attribute_fixes": 0,
            "fixes_details": [],
        }

    def get_xml_fixes_stats(self) -> dict[str, Any]:
        """Возвращает статистику примененных XML исправлений."""
        return self.xml_fixes_stats.copy()

    def _preprocess_xml(self, source: Union[Path, io.BytesIO]) -> bytes:
        """Предварительная обработка XML для исправления проблем с namespace и кодировкой."""
        try:
            # Читаем содержимое
            if isinstance(source, Path):
                with open(source, "rb") as f:
                    content = f.read()
            else:
                source.seek(0)  # Убеждаемся, что читаем с начала
                content = source.read()
                source.seek(0)  # Возвращаем указатель на начало

            # Декодируем в строку для обработки
            try:
                xml_str = content.decode("utf-8")
            except UnicodeDecodeError:
                # Пробуем другие кодировки
                for encoding in ["cp1251", "iso-8859-1", "latin1"]:
                    try:
                        xml_str = content.decode(encoding)
                        self.logger.warning(f"XML декодирован с кодировкой {encoding} вместо UTF-8")
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    # Если ничего не помогло, используем замену ошибочных символов
                    xml_str = content.decode("utf-8", errors="replace")
                    self.logger.warning("XML декодирован с заменой ошибочных символов")

            # РАСШИРЕННАЯ обработка XML проблем
            xml_str = self._fix_xml_issues(xml_str)

            return xml_str.encode("utf-8")

        except Exception as e:
            self.logger.error(f"Ошибка предварительной обработки XML: {e}")
            # В случае ошибки возвращаем исходное содержимое
            if isinstance(source, Path):
                with open(source, "rb") as f:
                    return f.read()
            else:
                source.seek(0)
                content = source.read()
                source.seek(0)
                return content

    def _fix_xml_issues(self, xml_str: str) -> str:
        """РАСШИРЕННАЯ обработка XML проблем для поврежденных FB2 файлов."""

        # 1. Исправляем проблемы с namespace
        xml_str = self._fix_namespace_issues(xml_str)

        # 2. Исправляем невалидные символы в XML
        xml_str = self._fix_invalid_xml_chars(xml_str)

        # 3. Исправляем несоответствие тегов
        xml_str = self._fix_mismatched_tags(xml_str)

        # 4. Исправляем невалидные атрибуты
        xml_str = self._fix_invalid_attributes(xml_str)

        # 5. Исправляем структурные проблемы
        xml_str = self._fix_structural_issues(xml_str)

        return xml_str

    def _fix_namespace_issues(self, xml_str: str) -> str:
        """Консервативно исправляет только известные проблемы с namespace в XML."""
        import re

        # Исправляем только конкретную проблему: l:href без объявления namespace
        if "l:href=" in xml_str:
            # Находим корневой элемент FictionBook
            root_match = re.search(r"<FictionBook[^>]*>", xml_str)
            if root_match:
                root_tag = root_match.group(0)

                # Проверяем, есть ли уже объявление для префикса 'l'
                if "xmlns:l=" not in root_tag:
                    # Добавляем объявление namespace для префикса 'l'
                    if 'xmlns:xlink="http://www.w3.org/1999/xlink"' in root_tag:
                        # Добавляем рядом с существующим xlink
                        new_root_tag = root_tag.replace(
                            'xmlns:xlink="http://www.w3.org/1999/xlink"',
                            'xmlns:xlink="http://www.w3.org/1999/xlink" xmlns:l="http://www.w3.org/1999/xlink"',
                        )
                    else:
                        # Добавляем оба namespace
                        new_root_tag = root_tag.replace(
                            ">", ' xmlns:l="http://www.w3.org/1999/xlink" xmlns:xlink="http://www.w3.org/1999/xlink">'
                        )

                    xml_str = xml_str.replace(root_tag, new_root_tag)
                    self.logger.warning("Добавлено объявление namespace для префикса 'l:' (исправление l:href)")

        return xml_str

    def _fix_invalid_xml_chars(self, xml_str: str) -> str:
        """Исправляет невалидные символы в XML."""
        import re

        # Удаляем управляющие символы, которые недопустимы в XML
        # Оставляем только допустимые: \t (0x09), \n (0x0A), \r (0x0D) и символы >= 0x20
        xml_str = re.sub(r"[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]", "", xml_str)

        # Исправляем неэкранированные амперсанды (& без ; в конце)
        xml_str = re.sub(r"&(?![a-zA-Z0-9#]+;)", "&amp;", xml_str)

        # Исправляем неэкранированные < и > в текстовом содержимом
        # Это сложная задача, поэтому делаем консервативно

        return xml_str

    def _fix_mismatched_tags(self, xml_str: str) -> str:
        """Исправляет несоответствие открывающих/закрывающих тегов с умной логикой."""
        import re

        # 1. Исправляем конкретные паттерны неправильной вложенности параграфов
        xml_str = self._fix_paragraph_nesting(xml_str)

        # 2. Простая проверка баланса тегов для основных FB2 элементов
        fb2_tags = ["p", "strong", "emphasis", "section", "title", "subtitle"]

        for tag_name in fb2_tags:
            # Подсчитываем открывающие и закрывающие теги
            open_count = len(re.findall(f"<{tag_name}[^/>]*(?<!/)>", xml_str))
            close_count = len(re.findall(f"</{tag_name}>", xml_str))

            # Если закрывающих больше, удаляем лишние
            if close_count > open_count:
                excess = close_count - open_count
                for _ in range(excess):
                    # Удаляем последний лишний закрывающий тег
                    xml_str = re.sub(f"</{tag_name}>", "", xml_str, count=1)

                self.logger.warning(f"Удалено {excess} лишних закрывающих тегов </{tag_name}>")

        return xml_str

    def _fix_paragraph_nesting(self, xml_str: str) -> str:
        """Исправляет специфичные проблемы с вложенностью параграфов.

        ОПТИМИЗИРОВАННАЯ ВЕРСИЯ: Объединенные regex операции для максимальной производительности.
        """
        import re

        # Счетчики для логирования
        fixes_count = {
            "nested_paragraphs": 0,
            "empty_paragraphs": 0,
            "multiple_opening": 0,
            "emphasis_quotes": 0,
            "orphan_quotes": 0,
            "missing_p_tags": 0,
        }

        # ОПТИМИЗАЦИЯ 1: Объединенные regex операции в один проход
        # Определяем все паттерны для замены
        replacement_patterns = [
            # Исправляем паттерн: </p><p>...много контента...</p></p>
            (r"</p><p>(.*?)</p></p>", r"<p>\1</p>", "nested_paragraphs"),
            # Исправляем самозакрывающиеся параграфы <p/> -> удаляем их
            (r"<p\s*/>", "", "empty_paragraphs"),
            # Исправляем множественные открывающие теги <p><p><p>
            (r"(<p[^>]*>)\s*(<p[^>]*>)+", r"\1", "multiple_opening"),
            # Исправляем одиночные закрывающие теги </p> на отдельных строках
            ##(r"^\s*</p>\s*$", "", "orphan_close_p"),
            # Исправляем одиночные закрывающие теги </section> без соответствующих открывающих
            ##(r"^\s*</section>\s*$", "", "orphan_close_section"),
            # Исправляем проблемы с emphasis внутри параграфов
            (r'<emphasis>([^<]*?)([»"])</emphasis></p>', r"<emphasis>\1</emphasis>\2</p>", "emphasis_quotes"),
            # Исправляем проблему с лишними кавычками после emphasis
            (r'</emphasis>([»"])</p>', r"</emphasis></p>", "orphan_quotes"),
        ]

        # Применяем все паттерны в едином цикле
        for pattern, replacement, fix_type in replacement_patterns:
            flags = re.DOTALL | re.MULTILINE if fix_type in ["orphan_close_p", "orphan_close_section"] else re.DOTALL

            # Подсчитываем количество совпадений перед заменой
            matches = re.findall(pattern, xml_str, flags=flags)
            if matches:
                fixes_count[fix_type] = len(matches)
                xml_str = re.sub(pattern, replacement, xml_str, flags=flags)

        # ОПТИМИЗАЦИЯ 2: Замена построчной обработки на regex
        # Исправляем проблему с отсутствующими открывающими <p> тегами
        # Паттерн: строка заканчивается на </emphasis></p>, но не начинается с <p>
        missing_p_pattern = r"^(\s*)(?!<p>)([^<\n]*<emphasis>.*?</emphasis></p>)$"
        missing_p_matches = re.findall(missing_p_pattern, xml_str, re.MULTILINE)

        if missing_p_matches:
            # Проверяем контекст - не является ли это продолжением предыдущей строки
            def replace_missing_p(match):
                indent, content = match.groups()
                # Простая эвристика: если строка начинается с заглавной буквы или кавычки, добавляем <p>
                if content.strip() and (content.strip()[0].isupper() or content.strip()[0] in '«"'):
                    return f"{indent}<p>{content}"
                return f"{indent}{content}"

            xml_str = re.sub(missing_p_pattern, replace_missing_p, xml_str, flags=re.MULTILINE)
            fixes_count["missing_p_tags"] = len(missing_p_matches)

        # Логирование результатов и сбор статистики
        total_paragraph_fixes = 0
        for fix_type, count in fixes_count.items():
            if count > 0:
                total_paragraph_fixes += count
                fix_messages = {
                    "nested_paragraphs": f"Исправлено {count} случаев неправильной вложенности параграфов",
                    "empty_paragraphs": f"Удалено {count} пустых самозакрывающихся параграфов <p/>",
                    "multiple_opening": f"Исправлено {count} случаев множественных открывающих тегов <p>",
                    "emphasis_quotes": f"Исправлено {count} случаев кавычек внутри emphasis",
                    "orphan_quotes": f"Удалено {count} лишних кавычек после emphasis",
                    "missing_p_tags": f"Добавлено {count} отсутствующих открывающих тегов <p>",
                }
                self.logger.warning(fix_messages[fix_type])

                # Сохраняем детали для статистики
                self.xml_fixes_stats["fixes_details"].append(
                    {"type": "paragraph", "subtype": fix_type, "count": count, "message": fix_messages[fix_type]}
                )

        # Обновляем общую статистику
        if total_paragraph_fixes > 0:
            self.xml_fixes_stats["paragraph_fixes"] += total_paragraph_fixes
            self.xml_fixes_stats["total_fixes_applied"] += total_paragraph_fixes

        return xml_str

    def _fix_invalid_attributes(self, xml_str: str) -> str:
        """Исправляет невалидные атрибуты в тегах.

        ОПТИМИЗИРОВАННАЯ ВЕРСИЯ: Объединенная обработка URL и атрибутов.
        """
        import re

        # ОПТИМИЗАЦИЯ: Объединенная обработка всех проблем с src-url в один проход
        src_url_patterns = [
            # Исправляем конкретную проблему: art="число</src-url">
            (r'(\?art=)"(\d+)(</src-url>)">', r"\1\2\3>"),
            # Исправляем другие варианты проблемных кавычек в src-url
            (r'(\?art=)"(\d+)(?=</src-url>)', r"\1\2"),
            # Исправляем лишние кавычки после закрывающих тегов
            (r'(</src-url)">', r"\1>"),
        ]

        src_url_fixes = 0
        for pattern, replacement in src_url_patterns:
            matches = re.findall(pattern, xml_str)
            if matches:
                xml_str = re.sub(pattern, replacement, xml_str)
                src_url_fixes += len(matches)

        if src_url_fixes > 0:
            message = f"Исправлено {src_url_fixes} проблемных кавычек в src-url"
            self.logger.warning(message)

            # Сохраняем детали для статистики
            self.xml_fixes_stats["fixes_details"].append(
                {"type": "attribute", "subtype": "src_url_quotes", "count": src_url_fixes, "message": message}
            )

            # Обновляем общую статистику
            self.xml_fixes_stats["attribute_fixes"] += src_url_fixes
            self.xml_fixes_stats["total_fixes_applied"] += src_url_fixes

        # ОПТИМИЗАЦИЯ: Улучшенная защита URL с кэшированием
        url_placeholders = {}
        url_counter = 0

        def protect_url(match):
            nonlocal url_counter
            placeholder = f"__URL_PLACEHOLDER_{url_counter}__"
            url_placeholders[placeholder] = match.group(0)
            url_counter += 1
            return placeholder

        # Защищаем содержимое src-url тегов
        xml_str = re.sub(r"<src-url>([^<]+)</src-url>", protect_url, xml_str)

        # Исправляем атрибуты без кавычек
        xml_str = re.sub(r'(\w+)=([^"\s>]+)(?=\s|>)', r'\1="\2"', xml_str)

        # Восстанавливаем URL (оптимизированная версия)
        if url_placeholders:
            for placeholder, original in url_placeholders.items():
                xml_str = xml_str.replace(placeholder, original)

        # ОПТИМИЗАЦИЯ: Улучшенное удаление дублирующихся атрибутов
        # Используем более эффективный подход с предварительной проверкой
        if "=" in xml_str and '"' in xml_str:  # Быстрая проверка наличия атрибутов

            def remove_duplicate_attrs(match):
                tag_content = match.group(1)

                # Быстрая проверка - есть ли дублирующиеся атрибуты
                if tag_content.count("=") <= 1:
                    return match.group(0)  # Нет смысла обрабатывать

                attrs = {}
                attr_pattern = r'(\w+)="([^"]*)"'

                def replace_attr(attr_match):
                    attr_name, attr_value = attr_match.groups()
                    if attr_name not in attrs:
                        attrs[attr_name] = attr_value
                        return attr_match.group(0)
                    return ""  # Удаляем дубликат

                cleaned_content = re.sub(attr_pattern, replace_attr, tag_content)
                return f"<{cleaned_content}>"

            xml_str = re.sub(r"<([^>]+)>", remove_duplicate_attrs, xml_str)

        return xml_str

    def _fix_structural_issues(self, xml_str: str) -> str:
        """Исправляет структурные проблемы в FB2 файлах.

        ОПТИМИЗИРОВАННАЯ ВЕРСИЯ: Объединенный анализ тегов и эффективное удаление.
        """
        import re

        # ОПТИМИЗАЦИЯ: Объединенный анализ всех структурных тегов в один проход
        structural_tags = {
            "body": (r"<body[^>]*>", r"</body>", 7),
            "FictionBook": (r"<FictionBook[^>]*>", r"</FictionBook>", 14),
            "image": (r"<image[^/>]*(?<!/)>", r"</image>", 8),
            "empty-line": (r"<empty-line[^/>]*(?<!/)>", r"</empty-line>", 13),
            "subtitle": (r"<subtitle[^/>]*(?<!/)>", r"</subtitle>", 11),
            "cite": (r"<cite[^/>]*(?<!/)>", r"</cite>", 7),
        }

        fixes_applied = {}

        # Анализируем все теги в едином цикле
        for tag_name, (open_pattern, close_pattern, close_tag_len) in structural_tags.items():
            # Подсчитываем открывающие и закрывающие теги
            open_matches = re.findall(open_pattern, xml_str)
            close_matches = re.findall(close_pattern, xml_str)

            open_count = len(open_matches)
            close_count = len(close_matches)

            if close_count > open_count:
                excess = close_count - open_count

                # ОПТИМИЗАЦИЯ: Удаляем лишние теги за один проход с конца
                # Находим все позиции закрывающих тегов
                close_positions = []
                start_pos = 0
                while True:
                    pos = xml_str.find(close_pattern.replace(r"</", "</"), start_pos)
                    if pos == -1:
                        break
                    close_positions.append(pos)
                    start_pos = pos + 1

                # Удаляем лишние теги с конца (сохраняем порядок)
                if close_positions and excess > 0:
                    # Сортируем позиции по убыванию для удаления с конца
                    close_positions.sort(reverse=True)

                    removed_count = 0
                    for pos in close_positions[:excess]:
                        # Проверяем, что тег еще существует на этой позиции
                        close_tag = f"</{tag_name}>"
                        if xml_str[pos : pos + close_tag_len] == close_tag:
                            xml_str = xml_str[:pos] + xml_str[pos + close_tag_len :]
                            removed_count += 1
                            if removed_count >= excess:
                                break

                    if removed_count > 0:
                        fixes_applied[tag_name] = removed_count

        # Логирование результатов и сбор статистики
        if fixes_applied:
            total_structural_fixes = sum(fixes_applied.values())

            # Отдельное логирование для основных тегов
            for tag_name in ["body", "FictionBook"]:
                if tag_name in fixes_applied:
                    message = f"Удалено {fixes_applied[tag_name]} лишних закрывающих тегов </{tag_name}>"
                    self.logger.warning(message)

                    # Сохраняем детали для статистики
                    self.xml_fixes_stats["fixes_details"].append(
                        {
                            "type": "structural",
                            "subtype": f"{tag_name}_tags",
                            "count": fixes_applied[tag_name],
                            "message": message,
                        }
                    )

            # Суммарное логирование для остальных тегов
            other_tags_total = sum(count for tag, count in fixes_applied.items() if tag not in ["body", "FictionBook"])
            if other_tags_total > 0:
                message = f"Удалено {other_tags_total} лишних закрывающих тегов различных типов"
                self.logger.warning(message)

                # Сохраняем детали для статистики
                self.xml_fixes_stats["fixes_details"].append(
                    {"type": "structural", "subtype": "other_tags", "count": other_tags_total, "message": message}
                )

            # Обновляем общую статистику
            self.xml_fixes_stats["structural_fixes"] += total_structural_fixes
            self.xml_fixes_stats["total_fixes_applied"] += total_structural_fixes

        return xml_str

    def parse(self, source: Union[Path, io.BytesIO]) -> FB2Book:
        """Основной метод для парсинга FB2 файла.

        Args:
            file_path: Путь к FB2 файлу.

        Returns:
            Экземпляр FB2Book с данными из файла.

        Raises:
            QuarantineError: Если файл поврежден или не является FB2.

        """
        try:
            # Определяем имя источника для логирования
            filename = source.name if isinstance(source, Path) else getattr(source, "name", "<stream>")

            self.logger.info(f"Начинаем парсинг FB2: {filename}")
            self.binaries = {}  # Сбрасываем для каждого файла
            self.reset_xml_fixes_stats()  # Сбрасываем статистику исправлений

            # Используем defusedxml.iterparse для безопасного парсинга XML.
            # Для Path передаем строковый путь, для BytesIO – сам поток.
            try:
                context = (
                    ET_safe.iterparse(str(source), events=("start", "end"))
                    if isinstance(source, Path)
                    else ET_safe.iterparse(source, events=("start", "end"))
                )
                _, root = next(context)  # Получаем корневой элемент

                for event, elem in context:
                    if event == "end":
                        elem.tag = NS_CLEANUP_REGEX.sub("", elem.tag)  # Очищаем namespace

            except Exception as e:
                # Проверяем, является ли это ошибкой парсинга XML
                error_str = str(e).lower()
                error_type = str(type(e))

                xml_parse_errors = [
                    "unbound prefix",
                    "parseerror",
                    "not well-formed",
                    "mismatched tag",
                    "invalid token",
                    "xmlsyntaxerror",
                ]

                is_xml_error = any(
                    error_pattern in error_str or error_pattern in error_type.lower()
                    for error_pattern in xml_parse_errors
                )

                # if is_xml_error:
                #     # Если стандартный парсинг не удался, пробуем с предварительной обработкой
                #     self.logger.warning(f"Обнаружена проблема с XML, применяем исправление: {e}")

                #     try:
                #         xml_content = self._preprocess_xml(source)
                #         context = ET_safe.iterparse(io.BytesIO(xml_content), events=("start", "end"))
                #         _, root = next(context)  # Получаем корневой элемент

                #         for event, elem in context:
                #             if event == "end":
                #                 elem.tag = NS_CLEANUP_REGEX.sub("", elem.tag)  # Очищаем namespace

                #         self.logger.info("XML успешно исправлен и распарсен")

                #     except Exception as repair_error:
                #         # Если и исправление не помогло, пробуем последний шанс - lxml
                #         self.logger.warning(f"Исправление XML не помогло: {repair_error}. Пробуем lxml с recover=True")

                #         try:
                #             # Используем lxml с режимом восстановления
                #             from lxml import etree

                #             if isinstance(source, Path):
                #                 with open(source, "rb") as f:
                #                     content = f.read()
                #             else:
                #                 source.seek(0)
                #                 content = source.read()
                #                 source.seek(0)

                #             # Парсим с режимом восстановления
                #             parser = etree.XMLParser(recover=True, encoding="utf-8")
                #             root = etree.fromstring(content, parser)

                #             # Очищаем namespace в тегах
                #             for elem in root.iter():
                #                 elem.tag = NS_CLEANUP_REGEX.sub("", elem.tag)

                #             self.logger.info("XML восстановлен с помощью lxml recover=True")

                #         except Exception as lxml_error:
                #             self.logger.error(f"Все попытки восстановления XML провалились: {lxml_error}")
                #             raise QuarantineError(f"Невозможно распарсить поврежденный XML: {e}") from e
                # else:
                #     raise
                
                raise  # Просто пробрасываем исходную ошибку без попыток исправления


            # 1. Парсим метаданные (<description>)
            description_elem = root.find("description")
            if description_elem is None:
                raise QuarantineError("Отсутствует обязательный тег <description> в FB2 файле.")
            description = self._parse_description(description_elem)

            # 2. Парсим тело книги (<body>), ИСКЛЮЧАЯ блоки сносок
            # Исключаем все варианты: 'notes', 'footnotes'
            footnote_body_names = {"notes", "footnotes"}
            bodies = [self._parse_body(b) for b in root.findall("body") if b.get("name") not in footnote_body_names]

            # 3. Извлекаем сноски из <body name="notes">
            footnotes = self._extract_footnotes(root)

            return FB2Book(
                description=description,
                bodies=bodies,
                binaries=self.binaries,
                footnotes=footnotes,
            )

        except (ET.ParseError, getattr(ET, "XMLSyntaxError", ET.ParseError)) as e:
            # lxml может выбрасывать как ParseError так и XMLSyntaxError
            raise QuarantineError(f"Ошибка парсинга XML в источнике {filename}: {e}") from e
        except Exception as e:
            if not isinstance(e, QuarantineError):
                self.logger.error(f"Неожиданная ошибка при парсинге FB2: {e}", exc_info=True)
                raise QuarantineError(f"Не удалось обработать FB2 источник: {e}") from e
            raise

    def _get_text(self, element: ET.Element) -> Optional[str]:
        """Безопасно получает текст элемента."""
        return element.text.strip() if element is not None and element.text else None

    def _get_attr(self, element: ET.Element, attr_name: str) -> Optional[str]:
        """Безопасно получает атрибут элемента, включая варианты с namespace и нестандартные префиксы."""
        if element is None:
            return None

        # Проверяем основной атрибут и стандартные варианты
        standard_variants = [
            attr_name,  # href
            f"{{http://www.w3.org/1999/xlink}}{attr_name}",  # {http://www.w3.org/1999/xlink}href
            f"xlink:{attr_name}",  # xlink:href (если не очищен namespace)
        ]

        for key in standard_variants:
            if key in element.attrib:
                return element.attrib[key]

        # Fallback: ищем любой атрибут, заканчивающийся на attr_name (для нестандартных префиксов)
        # Это поможет обработать случаи типа l:href, link:href и т.д.
        for key in element.attrib:
            if key.endswith(f":{attr_name}") or key.endswith(f"}}{attr_name}"):
                self.logger.warning(f"Найден нестандартный атрибут '{key}' вместо стандартного '{attr_name}'")
                return element.attrib[key]

        return None

    def _parse_binary(self, element: ET.Element):
        """Парсит тег <binary> и сохраняет данные."""
        bin_id = element.get("id")
        content_type = element.get("content-type")
        text = self._get_text(element)
        if not all([bin_id, content_type, text]):
            return  # Пропускаем неполные бинарные теги

        try:
            data = base64.b64decode(text or "")
            self.binaries[bin_id] = BinaryData(id=bin_id, content_type=content_type, data=data)
        except Exception as e:
            self.logger.warning(f"Не удалось декодировать бинарные данные для id='{bin_id}': {e}")

    def _parse_description(self, element: ET.Element) -> Description:
        """Парсит тег <description>."""
        return Description(
            title_info=self._parse_title_info(element.find("title-info")),
            document_info=self._parse_document_info(element.find("document-info")),
            publish_info=self._parse_publish_info(element.find("publish-info")),
        )

    def _parse_title_info(self, element: Optional[ET.Element]) -> Optional[TitleInfo]:
        """Парсит тег <title-info>."""
        if element is None:
            return None
        return TitleInfo(
            genres=[Genre(text=self._get_text(g) or "") for g in element.findall("genre") if self._get_text(g)],
            authors=[self._parse_author(a) for a in element.findall("author")],
            book_title=self._get_text(element.find("book-title")),
            annotation=self._parse_annotation(element.find("annotation")),
            keywords=self._get_text(element.find("keywords")),
            date=self._parse_date(element.find("date")),
            lang=self._get_text(element.find("lang")),
            src_lang=self._get_text(element.find("src-lang")),
            translators=[self._parse_author(a) for a in element.findall("translator")],
            sequences=[self._parse_sequence(s) for s in element.findall("sequence") if s.get("name")],
            coverpage=None,
        )

    def _parse_author(self, element: ET.Element) -> Author:
        """Парсит тег <author> или <translator>."""
        return Author(
            first_name=self._get_text(element.find("first-name")),
            middle_name=self._get_text(element.find("middle-name")),
            last_name=self._get_text(element.find("last-name")),
            nickname=self._get_text(element.find("nickname")),
            email=self._get_text(element.find("email")),
            home_page=self._get_text(element.find("home-page")),
            author_id=self._get_text(element.find("id")),
        )

    def _parse_sequence(self, element: ET.Element) -> SequenceInfo:
        """Парсит тег <sequence>."""
        number_str = element.get("number")
        return SequenceInfo(
            name=element.get("name", ""),
            number=int(number_str) if number_str and number_str.isdigit() else None,
        )

    def _parse_date(self, element: Optional[ET.Element]) -> Optional[DateInfo]:
        """Парсит тег <date>."""
        if element is None:
            return None
        return DateInfo(value=element.get("value"), text=self._get_text(element))

    def _parse_image(self, element: Optional[ET.Element]) -> Optional[Image]:
        """Парсит тег <image>."""
        if element is None:
            return None
        href = self._get_attr(element, "href")
        if href:
            # Убираем '#' из начала href, если он есть
            return Image(href=href.lstrip("#"))
        return None

    def _parse_annotation(self, element: Optional[ET.Element]) -> Optional[Annotation]:
        """Парсит тег <annotation>."""
        if element is None:
            return None

        from .fb2_model import AnnotationElement

        content_list: list[AnnotationElement] = []
        for child in element:
            tag = child.tag
            if tag == "p":
                content_list.append(self._parse_paragraph(child))
            elif tag == "poem":
                content_list.append(self._parse_poem(child))
            elif tag == "cite":
                content_list.append(self._parse_cite(child))
            elif tag == "image":
                # Картинки полностью игнорируем
                continue
            elif tag == "empty-line":
                # Пустые строки игнорируем
                continue
            # Остальные неизвестные теги тоже игнорируем

        return Annotation(elements=content_list)

    def _get_full_text_content(self, element: ET.Element) -> str:
        """Рекурсивно извлекает весь текстовый контент из элемента, включая вложенные теги."""
        text_parts = []

        # Добавляем текст самого элемента
        if element.text:
            text_parts.append(element.text.strip())

        # Рекурсивно обрабатываем всех потомков
        for child in element:
            child_text = self._get_full_text_content(child)
            if child_text:
                text_parts.append(child_text)

            # Добавляем tail текст (текст после закрывающего тега потомка)
            if child.tail:
                text_parts.append(child.tail.strip())

        return " ".join(filter(None, text_parts))

    def _parse_paragraph_content(self, element: ET.Element) -> list[ParagraphContent]:
        """Рекурсивно парсит содержимое элемента, обрабатывая все FB2 теги форматирования."""
        content = []

        # Добавляем текст элемента перед первым дочерним элементом
        if element.text:
            text = element.text.strip()
            if text:
                content.append(text)

        # Рекурсивно обрабатываем каждый дочерний элемент
        for child in element:
            parsed_element = self._parse_format_element(child)
            if parsed_element is not None:
                content.append(parsed_element)

            # Добавляем tail текст (текст после закрывающего тега дочернего элемента)
            if child.tail:
                tail = child.tail.strip()
                if tail:
                    content.append(tail)

        return content

    def _parse_format_element(self, element: ET.Element) -> Optional[ParagraphContent]:
        """Парсит отдельный элемент форматирования с полной поддержкой вложенности."""
        tag = element.tag

        if tag == "strong":
            # Проверяем на вложенные элементы для комбинированного форматирования
            emphasis_child = element.find("emphasis")
            if emphasis_child is not None:
                from .fb2_model import BoldItalic

                # Рекурсивно обрабатываем содержимое emphasis
                # nested_content = self._parse_paragraph_content(emphasis_child)
                nested_content = self._parse_paragraph_content(element)
                text = self._flatten_content_to_text(nested_content)
                return BoldItalic(text=text) if text else None
            else:
                from .fb2_model import Strong

                nested_content = self._parse_paragraph_content(element)
                text = self._flatten_content_to_text(nested_content)
                return Strong(text=text) if text else None

        elif tag == "emphasis":
            # Обрабатываем только простое курсивное форматирование
            # ВАЖНО: Комбинированное форматирование <emphasis><strong> НЕ обрабатывается здесь!
            # Оно обрабатывается в блоке "strong" для единообразия (приоритет у внешнего тега).
            # Исправлено: убрана дублирующая проверка strong_child (недостижимый код).
            from .fb2_model import Emphasis

            nested_content = self._parse_paragraph_content(element)
            text = self._flatten_content_to_text(nested_content)
            return Emphasis(text=text) if text else None

        elif tag == "sub":
            from .fb2_model import Subscript

            text = self._get_full_text_content(element)
            return Subscript(text=text) if text else None

        elif tag == "sup":
            from .fb2_model import Superscript

            text = self._get_full_text_content(element)
            return Superscript(text=text) if text else None

        elif tag == "strikethrough":
            from .fb2_model import Strikethrough

            text = self._get_full_text_content(element)
            return Strikethrough(text=text) if text else None

        elif tag == "code":
            from .fb2_model import Code

            text = self._get_full_text_content(element)
            return Code(text=text) if text else None

        elif tag == "style":
            from .fb2_model import Style

            name = element.get("name")
            text = self._get_full_text_content(element)
            return Style(name=name, text=text) if text else None

        elif tag == "a":
            # Обрабатываем ссылки и сноски
            href = self._get_attr(element, "href")
            link_type = element.get("type")
            text = self._get_full_text_content(element)

            if href:
                if link_type == "note":
                    from .fb2_model import Note

                    return Note(href=href, text=text)
                else:
                    from .fb2_model import Link

                    return Link(href=href, text=text)
        else:
            # Для неизвестных тегов просто извлекаем текст
            text = self._get_full_text_content(element)
            return text if text else None

        return None

    def _flatten_content_to_text(self, content: list[ParagraphContent]) -> str:
        """Преобразует список контента в плоский текст для простых элементов форматирования."""
        text_parts = []
        for item in content:
            if isinstance(item, str):
                text_parts.append(item)
            elif hasattr(item, "text") and item.text:
                text_parts.append(item.text)
            # Для сложных элементов просто игнорируем, чтобы избежать двойной вложенности
        return " ".join(filter(None, text_parts))

    def _parse_paragraph(self, element: ET.Element) -> Paragraph:
        """Парсит тег <p> с полной обработкой всех FB2 элементов форматирования и вложенности."""
        content = self._parse_paragraph_content(element)
        paragraph_id = element.get("id")  # Извлекаем атрибут id если есть
        return Paragraph(content=content, id=paragraph_id)

    def _parse_poem(self, element: ET.Element) -> Poem:
        """Парсит тег <poem>."""
        stanzas = []
        for stanza_elem in element.findall("stanza"):
            lines = [PoemVerse(text=self._get_full_text_content(v)) for v in stanza_elem.findall("v")]
            stanzas.append(PoemStanza(lines=lines))

        return Poem(
            stanzas=stanzas,
            title=self._get_text(element.find("title/p")),
            text_author=self._get_text(element.find("text-author")),
        )

    def _parse_cite(self, element: ET.Element) -> Cite:
        """Парсит тег <cite>."""
        paragraphs = []
        for child in element:
            if child.tag == "p":
                paragraphs.append(self._parse_paragraph(child))
            elif child.tag == "text-author":
                # Обрабатываем text-author как обычный параграф
                text_content = self._get_text(child)
                if text_content:
                    # Создаем параграф с текстом автора
                    from .fb2_model import Paragraph

                    author_paragraph = Paragraph(content=[f" — {text_content}"])
                    paragraphs.append(author_paragraph)

        return Cite(
            paragraphs=paragraphs,
            text_author=None,  # Больше не используем отдельное поле
        )

    def _parse_subtitle(self, element: ET.Element) -> Subtitle:
        """Парсит тег <subtitle>."""
        from .fb2_model import Subtitle

        content: list[str] = []

        # Обрабатываем все дочерние элементы subtitle
        for child in element:
            if child.tag == "strong":
                # Для элементов форматирования сохраняем только текст
                text = self._get_full_text_content(child)
                if text:
                    content.append(text)
            # Для других тегов тоже берем текст
            else:
                text = self._get_full_text_content(child)
                if text:
                    content.append(text)

        # Если нет дочерних элементов, берем прямой текст
        if not content and element.text:
            content.append(element.text.strip())

        return Subtitle(content=content)

    def _parse_document_info(self, element: Optional[ET.Element]) -> Optional[DocumentInfo]:
        # Реализация парсинга <document-info> (по аналогии с title-info)
        if element is None:
            return None
        return DocumentInfo(
            authors=[self._parse_author(a) for a in element.findall("author")],
            program_used=self._get_text(element.find("program-used")),
            date=self._parse_date(element.find("date")),
            src_urls=[self._get_text(u) or "" for u in element.findall("src-url")],
            doc_id=self._get_text(element.find("id")),
            version=self._get_text(element.find("version")),
        )

    def _parse_publish_info(self, element: Optional[ET.Element]) -> Optional[PublishInfo]:
        # Реализация парсинга <publish-info>
        if element is None:
            return None
        year_str = self._get_text(element.find("year"))
        return PublishInfo(
            book_name=self._get_text(element.find("book-name")),
            publisher=self._get_text(element.find("publisher")),
            city=self._get_text(element.find("city")),
            year=int(year_str) if year_str and year_str.isdigit() else None,
            isbn=self._get_text(element.find("isbn")),
        )

    def _parse_body(self, element: ET.Element) -> Body:
        """Парсит тег <body>."""
        sections = element.findall("section")
        self.logger.debug(f"🔍 Body парсинг: найдено {len(sections)} секций, всего дочерних элементов: {len(element)}")

        # Если есть параграфы на уровне body (из-за поврежденного XML),
        # создаем виртуальную секцию для них
        orphan_paragraphs = []
        for child in element:
            if child.tag == "p":
                orphan_paragraphs.append(child)

        if orphan_paragraphs:
            self.logger.warning(
                f"🔧 Найдено {len(orphan_paragraphs)} параграфов на уровне body, создаем виртуальную секцию"
            )

            # Создаем виртуальный элемент section для orphan параграфов
            virtual_section = ET.Element("section")
            for p in orphan_paragraphs:
                virtual_section.append(p)

            # Добавляем виртуальную секцию к обычным секциям
            sections.append(virtual_section)

        return Body(
            name=element.get("name"),
            title=self._parse_annotation(element.find("title")),
            sections=[self._parse_section(s) for s in sections],
        )

    def _parse_section(self, element: ET.Element) -> Section:
        """Рекурсивно парсит тег <section>."""
        content: list[Union[Section, AnnotationElement]] = []

        # Сначала обрабатываем прямых дочерних элементов, которые не являются вложенными секциями
        for child in element:
            if child.tag == "section":
                content.append(self._parse_section(child))  # Рекурсивный вызов
            elif child.tag == "p":
                content.append(self._parse_paragraph(child))
            elif child.tag == "poem":
                content.append(self._parse_poem(child))
            elif child.tag == "cite":
                content.append(self._parse_cite(child))
            elif child.tag == "epigraph":
                content.append(self._parse_cite(child))
            elif child.tag == "subtitle":
                content.append(self._parse_subtitle(child))
            elif child.tag == "image":
                # Картинки полностью игнорируем
                continue
            elif child.tag == "empty-line":
                # Пустые строки игнорируем
                continue
            # Остальные неизвестные теги тоже игнорируем

        return Section(title=self._parse_annotation(element.find("title")), content=content)

    def _extract_footnotes(self, root: ET.Element) -> dict[str, str]:
        """Извлекает сноски используя цепочку стратегий.

        Применяет стратегии в порядке приоритета до первого успешного результата.
        Это обеспечивает обработку как стандартных, так и нестандартных схем разметки сносок.

        Returns:
            Словарь {note_id: текст_сноски}
        """
        for strategy in self._footnote_strategies:
            footnotes = strategy(root)
            if footnotes:
                return footnotes
        return {}

    def _extract_footnotes_standard(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №1: Стандартная разметка <body name="notes"> или <body name="footnotes">.

        Ищет все <section id="note_X"> внутри <body name="notes"> или <body name="footnotes">.
        """
        footnotes = {}

        # Ищем все <body name="notes"> и <body name="footnotes">
        footnote_body_names = {"notes", "footnotes"}
        for body_name in footnote_body_names:
            for notes_body in root.findall(f".//body[@name='{body_name}']"):
                # В каждом теле сносок ищем секции с id
                # Добавляем ".//" для рекурсивного поиска на любую глубину вложенности
                for section in notes_body.findall(".//section"):
                    section_id = section.get("id")
                    if section_id:
                        # Извлекаем весь текстовый контент из секции
                        footnote_text = self._get_full_text_content(section)
                        if footnote_text:
                            footnotes[section_id] = footnote_text

        return footnotes

    def _extract_footnotes_from_paragraphs_in_body(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №1.5: Сноски в виде <p id="..."> внутри <body name="notes/footnotes">.

        Обрабатывает нестандартную разметку, где каждая сноска является параграфом с атрибутом id.
        """
        footnotes: dict[str, str] = {}

        footnote_body_names = {"notes", "footnotes"}

        for body_name in footnote_body_names:
            # Ищем блоки с заданным именем
            for notes_body in root.findall(f".//body[@name='{body_name}']"):
                # Ищем все параграфы, которые имеют атрибут id
                for p_tag in notes_body.findall(".//p[@id]"):
                    note_id = p_tag.get("id")
                    if not note_id:
                        continue

                    # Извлекаем полный текст параграфа
                    footnote_text = self._get_full_text_content(p_tag)
                    if not footnote_text:
                        continue

                    # Очищаем ведущий номер ("1. " или "1 ") при наличии
                    cleaned_text = re.sub(r"^\s*\d+\s*\.?:?\s*", "", footnote_text).strip()

                    if cleaned_text:
                        footnotes[note_id] = cleaned_text

        return footnotes

    def _extract_footnotes_by_section_title(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №2: Поиск по заголовку секции "Примечания/Notes".

        Ищет секции с заголовками типа "Примечания", "Notes", "Сноски" и извлекает
        из них вложенные секции с id как сноски.
        """
        footnotes = {}

        # Паттерны заголовков секций примечаний (регистронезависимо)
        notes_patterns = [
            re.compile(r"примечания", re.IGNORECASE),
            re.compile(r"сноски", re.IGNORECASE),
            re.compile(r"notes", re.IGNORECASE),
            re.compile(r"footnotes", re.IGNORECASE),
            re.compile(r"комментарии", re.IGNORECASE),
        ]

        # Ищем все секции во всех body (кроме notes)
        for body in root.findall("body"):
            if body.get("name") == "notes":
                continue  # Пропускаем стандартные блоки сносок

            for section in body.findall(".//section"):
                # Проверяем заголовок секции
                if section.find("title") is not None:
                    title_text = self._get_full_text_content(section.find("title")).lower()

                    # Проверяем соответствие паттернам примечаний
                    if any(pattern.search(title_text) for pattern in notes_patterns):
                        # Ищем вложенные секции с id
                        for nested_section in section.findall("section"):
                            section_id = nested_section.get("id")
                            if section_id:
                                footnote_text = self._get_full_text_content(nested_section)
                                if footnote_text:
                                    footnotes[section_id] = footnote_text

        return footnotes

    def _extract_footnotes_by_id_pattern(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №3: Поиск по паттерну id сносок во всем документе.

        Ищет все секции с id соответствующими паттернам сносок (n_X, note_X, fn_X).
        """
        footnotes = {}

        # Регулярные выражения для id сносок
        footnote_id_patterns = [
            re.compile(r"^n_\d+$"),  # n_1, n_2, ...
            re.compile(r"^note_?\d+$"),  # note_1, note1, ...
            re.compile(r"^fn_?\d+$"),  # fn_1, fn1, ...
            re.compile(r"^sn_?\d+$"),  # sn_1, sn1, ... (сноска)
        ]

        # Ищем все секции во всех body (кроме уже обработанных notes)
        for body in root.findall("body"):
            if body.get("name") == "notes":
                continue  # Пропускаем стандартные блоки сносок

            for section in body.findall(".//section"):
                section_id = section.get("id")
                if section_id:
                    # Проверяем соответствие паттернам id сносок
                    if any(pattern.match(section_id) for pattern in footnote_id_patterns):
                        footnote_text = self._get_full_text_content(section)
                        if footnote_text:
                            footnotes[section_id] = footnote_text

        return footnotes

    def _extract_footnotes_by_paragraph_sequence(self, root: ET.Element) -> dict[str, str]:
        """Стратегия №4: Поиск сносок как последовательности параграфов.

        Ищет заголовок "notes/сноски/примечания" в <p><strong>, затем извлекает
        последовательность: номер в <strong> -> текст сноски -> номер -> текст...
        """
        footnotes: dict[str, str] = {}

        # Паттерны заголовков сносок
        header_patterns = [
            re.compile(r"^notes$", re.IGNORECASE),
            re.compile(r"^сноски$", re.IGNORECASE),
            re.compile(r"^примечания$", re.IGNORECASE),
            re.compile(r"^footnotes$", re.IGNORECASE),
        ]

        # Паттерн для номеров сносок (только цифры в <strong>)
        number_pattern = re.compile(r"^\s*(\d+)\s*$")

        # Ищем во всех body (кроме notes)
        for body in root.findall("body"):
            if body.get("name") == "notes":
                continue

            # Получаем все параграфы из всех секций этого body
            all_paragraphs = []
            for section in body.findall(".//section"):
                for p in section.findall(".//p"):
                    all_paragraphs.append(p)

            # Ищем заголовок блока сносок
            header_found = False
            for i, p in enumerate(all_paragraphs):
                # Проверяем есть ли <strong> с заголовком сносок
                strong_elem = p.find("strong")
                if strong_elem is not None:
                    strong_text = self._get_full_text_content(strong_elem).strip().lower()

                    if any(pattern.match(strong_text) for pattern in header_patterns):
                        header_found = True
                        # Начинаем поиск сносок после заголовка
                        self._parse_footnote_sequence(all_paragraphs[i + 1 :], footnotes, number_pattern)
                        break

            if header_found:
                break  # Нашли блок сносок, не ищем в других body

        return footnotes

    def _parse_footnote_sequence(
        self,
        paragraphs: list[ET.Element],
        footnotes: dict[str, str],
        number_pattern: re.Pattern[str],
    ) -> None:
        """Вспомогательный метод для парсинга последовательности сносок."""
        i = 0
        while i < len(paragraphs):
            p = paragraphs[i]

            # Пропускаем empty-line и другие нетекстовые элементы
            if p.tag != "p":
                i += 1
                continue

            # Проверяем, есть ли номер сноски в <strong>
            strong_elem = p.find("strong")
            if strong_elem is not None:
                strong_text = self._get_full_text_content(strong_elem).strip()
                match = number_pattern.match(strong_text)

                if match:
                    footnote_number = match.group(1)

                    # Ищем текст сноски в следующих параграфах
                    footnote_text_parts = []
                    j = i + 1

                    while j < len(paragraphs):
                        next_p = paragraphs[j]

                        # Если встретили следующий номер в <strong>, останавливаемся
                        if next_p.tag == "p":
                            next_strong = next_p.find("strong")
                            if next_strong is not None:
                                next_strong_text = self._get_full_text_content(next_strong).strip()
                                if number_pattern.match(next_strong_text):
                                    break

                            # Добавляем текст параграфа к сноске
                            p_text = self._get_full_text_content(next_p).strip()
                            if p_text:
                                footnote_text_parts.append(p_text)

                        j += 1

                    # Сохраняем сноску если есть текст
                    if footnote_text_parts:
                        footnote_id = f"n_{footnote_number}"
                        footnotes[footnote_id] = " ".join(footnote_text_parts)

                    i = j  # Переходим к следующему номеру
                    continue

            i += 1
